<template>

</template>

<script>
export default {
  name: "foot.vue"
}
</script>

<style scoped>
.foot-container {
  background-color: #2d3748;
  padding: 30rpx 20rpx;
  margin-top: 40rpx;
  border-top: 1px solid #4a5568;
  text-align: center;
  font-size: 24rpx;
  color: #a0aec0;
  line-height: 1.6;
}

.contact-info {
  margin-bottom: 20rpx;
  text-align: center;
  line-height: 1.8;
}

.domain-info {
  font-weight: 500;
  color: #e2e8f0;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .foot-container {
    background-color: #2d3748;
    border-top-color: #4a5568;
    color: #a0aec0;
  }

  .domain-info {
    color: #e2e8f0;
  }
}
</style>