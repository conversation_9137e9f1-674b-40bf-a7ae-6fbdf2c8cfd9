<template>
  <ul class="list">
    <template v-if="videoList && videoList.length > 0">
      <li v-for="(item, index) in videoList" :key="index">
        <navigator :url="`/pages/view/view?id=${item.id}`">
          <view class="cover">
            <view class="img">
              <image :src="item.poster || item.videoPic || 'https://placehold.co/500x283'" mode="aspectFill"/>
            </view>
            <view class="video-info">
              <view class="pingfen" v-if="item.score || item.videoScore">
                {{ item.score || item.videoScore || '' }}
              </view>
              <view class="renqitime">
                <span>{{ formatHits(item.hits || item.videoHits || 0) }}</span>
                <span>{{ item.videoDuration || item.remarks || '' }}</span>
              </view>
            </view>
          </view>
          <view class="biaoti">{{ item.title || item.videoName }}</view>
        </navigator>
      </li>
    </template>
    <view class="no-data" v-if="videoList.length === 0">
      <text>暂无相关内容</text>
    </view>
  </ul>
</template>

<script>
export default {
  name: 'HorizontalVideoList',
  props: {
    videoList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 格式化播放量
    formatHits(hits) {
      if (!hits) return '0';

      const num = parseInt(hits);
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万';
      }
      return num.toString();
    }
  }
}
</script>

<style scoped lang="scss">
/* 横屏视频列表样式 */
.list {
  li {
    width: calc(50% - 10rpx);
    margin-bottom: 10rpx;
  }

  .cover {
    height: auto;
    position: relative;
  }

  .img {
    position: relative;
    width: 100%;
    /* 使用固定的 16:9 的宽高比例 */
    padding-bottom: 56.25%; /* 9/16 = 0.5625 = 56.25% */
    overflow: hidden;
  }

  .img image {
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: cover; /* 确保图片填充整个容器且不变形 */
  }
}
</style>
